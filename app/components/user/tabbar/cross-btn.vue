<script setup lang="ts">
const isCrossActive = ref(false)

const {
    fastingState,
    fastingText,
    getLatestLightFastingRecord,
} = useFasting(true)

const { token } = storeToRefs(useUserStore())

watch(token, (val) => {
    if (val) {
        getLatestLightFastingRecord()
    }
}, { immediate: true })

watch(isCrossActive, (val) => {
    if (val) {
        getLatestLightFastingRecord()
    }
})

const { remainingTime, setTime } = useTimer()

watch(fastingState, (val) => {
    if (val.isChallenge) {
        setTime(val.startAt, val.endAt)
    }
}, { immediate: true, deep: true })

async function handlePictureFoodAdd(food: FoodItem[], dietType: DietType, mealPicture: string) {
    await saveDietRecord(food, dietType, mealPicture)
    navigateTo('/user/checkin/food?dietCheck=1')
    showSuccessToast('保存成功')
}

function handleFastingClick() {
    isCrossActive.value = false
    if (fastingState.value.isChallenge) {
        navigateTo('/user/checkin/fasting')
    } else {
        navigateTo('/user/checkin/fasting/start')
    }
}
// 用于控制引导模式 - start
const isGuideMode = ref(false)

const shouldShowPopup = computed(() => {
    return isCrossActive.value || isGuideMode.value
})

function handleTriggerClick() {
    if (!isGuideMode.value) {
        isCrossActive.value = !isCrossActive.value
    }
}

function setGuideMode(enabled: boolean) {
    isGuideMode.value = enabled
}

onMounted(() => {
    window.addEventListener('guide:showStep5', () => {
        setGuideMode(true)
    })

    window.addEventListener('guide:hideStep6', () => {
        setGuideMode(false)
    })
})

onUnmounted(() => {
    window.removeEventListener('guide:showStep5', () => {})
    window.removeEventListener('guide:hideStep6', () => {})
})

defineExpose({
    setGuideMode,
})
// 用于控制引导模式 - end

const takePhotoRef = useTemplateRef('takePhotoRef')

function triggerFileUpload() {
    if (takePhotoRef.value) {
        takePhotoRef.value.manualTriggerUpload()
    }
}
</script>

<template>
    <!-- 需求暂时隐藏 -->
    <div
        v-if="false"
        class="card-popup px-24px py-16px"
        :class="shouldShowPopup ? '' : 'absolute translate-y-100000px'"
    >
        <div class="text-16px text-t-5 font-600">
            SLMC体重管家
        </div>

        <div class="mt-16px flex justify-between">
            <user-checkin-food-take-photo @add="handlePictureFoodAdd">
                <div class="w-140px h-160px rd-10px bg-warning-1 px-16px py-12px">
                    <div class="text-warning-6 text-16px font-600">
                        餐食拍照
                    </div>

                    <div class="text-t-3 text-12px mt-4px">
                        记录今日饮食
                    </div>

                    <div class="flex items-center justify-center">
                        <img src="@/assets/images/checkin/ai-photo.svg" class="mt-14px" alt="" srcset="" />
                    </div>
                </div>
            </user-checkin-food-take-photo>

            <div class="w-140px h-160px rd-10px px-16px py-12px" :class="fastingState.type === 0 ? 'bg-primary-1' : 'bg-warning-1'" @click="handleFastingClick">
                <div class="text-16px font-600" :class="fastingState.type === 0 ? 'text-primary-6' : 'text-warning-6'">
                    轻断食挑战
                </div>

                <div v-if="fastingState.isChallenge" class="mt-4px flex justify-between items-center">
                    <div class="text-t-4 font-600 text-12px">
                        {{ fastingText }}期
                    </div>

                    <div class="text-t-3 text-12px">
                        16-8模式
                    </div>
                </div>

                <div v-else class="text-t-3 text-12px">
                    立即加入挑战
                </div>

                <div class="flex flex-col items-center justify-center relative bottom-3px">
                    <img v-if="fastingState.type === 0" src="@/assets/images/checkin/fasting-2.svg" class="mt-14px" alt="" srcset="" />
                    <img v-else src="@/assets/images/checkin/fasting-3.svg" class="mt-14px" alt="" srcset="" />

                    <div v-if="fastingState.isChallenge" class="text-12px relative bottom-8px font-600" :class="fastingState.type === 0 ? 'text-primary-6' : 'text-warning-6'">
                        {{ remainingTime.hour }}:{{ remainingTime.minute }}:{{ remainingTime.second }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <user-checkin-food-take-photo
        ref="takePhotoRef"
        @add="handlePictureFoodAdd"
    >
        <div
            class="i-custom-tabbar-camera w-52px h-52px bg-#1D2229 rounded-full flex items-center justify-center relative bottom-24px"
            @click.stop="triggerFileUpload"
        >
        </div>
    </user-checkin-food-take-photo>

    <van-overlay :show="isCrossActive" />
</template>

<style lang="scss" scoped>
.card-popup {
  background: url('@/assets/images/checkin/circle-bg.png') no-repeat center center;
  background-size: 100% 100%;
  --uno: absolute bottom-62px left-16px w-343px h-253px z-10000;
}
</style>
